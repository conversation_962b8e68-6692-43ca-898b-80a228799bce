import pytest
from unittest.mock import MagicMock, patch
import copy
from datetime import datetime
from math import ceil

from app.services import agent_service
from commons.consts.api_codes import ApiCodes


class TestAgentServiceDLX:
    """测试agent_service中的DLX舱位功能"""

    def test_should_use_dlx_cabin_all_passengers_with_20kg_baggage(self):
        """测试should_use_dlx_cabin函数 - 所有乘客都有20kg行李"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_id': 'lstPaxItem1',
                        'from_select_value': 'lstPaxItem1/123/1/456',
                    }
                ],
            },
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_id': 'lstPaxItem2',
                        'from_select_value': 'lstPaxItem2/789/2/012',
                    }
                ],
            },
        ]

        result = agent_service.should_use_dlx_cabin(passengers)
        assert result is True

    def test_should_use_dlx_cabin_some_passengers_without_baggage(self):
        """测试should_use_dlx_cabin函数 - 部分乘客没有行李"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 20,
                        'from_select_id': 'lstPaxItem1',
                        'from_select_value': 'lstPaxItem1/123/1/456',
                    }
                ],
            },
            {'passenger_type': 'adult', 'baggages': []},  # 没有行李
        ]

        result = agent_service.should_use_dlx_cabin(passengers)
        assert result is False

    def test_should_use_dlx_cabin_non_20kg_baggage(self):
        """测试should_use_dlx_cabin函数 - 行李不是20kg"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'baggage',
                        'weight': 15,  # 不是20kg
                        'from_select_id': 'lstPaxItem1',
                        'from_select_value': 'lstPaxItem1/123/1/456',
                    }
                ],
            }
        ]

        result = agent_service.should_use_dlx_cabin(passengers)
        assert result is False

    def test_should_use_dlx_cabin_empty_passengers(self):
        """测试should_use_dlx_cabin函数 - 空乘客列表"""
        passengers = []

        result = agent_service.should_use_dlx_cabin(passengers)
        assert result is False

    def test_should_use_dlx_cabin_non_baggage_aux_type(self):
        """测试should_use_dlx_cabin函数 - 辅营类型不是行李"""
        passengers = [
            {
                'passenger_type': 'adult',
                'baggages': [
                    {
                        'aux_type': 'seat',  # 不是行李
                        'weight': 20,
                        'from_select_id': 'lstPaxItem1',
                        'from_select_value': 'lstPaxItem1/123/1/456',
                    }
                ],
            }
        ]

        result = agent_service.should_use_dlx_cabin(passengers)
        assert result is False

    @patch('app.services.agent_service.agent_helper')
    def test_book_with_use_dlx_cabin_true(self, mock_agent_helper):
        """测试book方法使用DLX舱位时跳过行李选择"""
        from app.services.agent_service import AgentService

        # 创建AgentService实例
        service = AgentService('test_user', 'test_pass', 'VJ')
        service.client = MagicMock()

        # 模拟各种返回值
        service.client.travel_options.return_value = 'travel_options_result'
        service.client.details.return_value = '预订页面 - 座位安置'
        service.client.addons.return_value = '预订页面 - 付款资讯'
        service.client.payment.return_value = '预订页面 - 确认选择'
        service.client.confirm.return_value = '预订页面 -旅程'
        service.client.processing.return_value = '预订页面 -旅程'

        # 模拟agent_helper的返回值
        mock_agent_helper.get_form_data.return_value = {'form_data': 'test'}
        mock_agent_helper.get_addons_form_data.return_value = [('key', 'value')]
        mock_agent_helper.get_book_fare.return_value = {'currency': 'USD', 'total_price': 100}
        mock_agent_helper.parse_book_result.return_value = {'pnr': 'ABC123'}

        passengers = [{'passenger_type': 'adult', 'first_name': 'Test', 'last_name': 'User'}]

        # 调用book方法，use_dlx_cabin=True
        result = service.book(fare_key='10,DLX_FARE_KEY', passengers=passengers, direct_pay=False, use_dlx_cabin=True)

        # 验证update_baggage_options没有被调用
        mock_agent_helper.update_baggage_options.assert_not_called()

        # 验证其他方法正常调用
        assert result['pnr'] == 'ABC123'

    @patch('app.services.agent_service.agent_helper')
    def test_book_with_use_dlx_cabin_false(self, mock_agent_helper):
        """测试book方法不使用DLX舱位时正常处理行李"""
        from app.services.agent_service import AgentService

        # 创建AgentService实例
        service = AgentService('test_user', 'test_pass', 'VJ')
        service.client = MagicMock()

        # 模拟各种返回值
        service.client.travel_options.return_value = 'travel_options_result'
        service.client.details.return_value = '预订页面 - 座位安置'
        service.client.addons.return_value = '预订页面 - 付款资讯'
        service.client.payment.return_value = '预订页面 - 确认选择'
        service.client.confirm.return_value = '预订页面 -旅程'
        service.client.processing.return_value = '预订页面 -旅程'

        # 模拟agent_helper的返回值
        mock_agent_helper.get_form_data.return_value = {'form_data': 'test'}
        mock_agent_helper.update_baggage_options.return_value = [{'passenger_type': 'adult'}]
        mock_agent_helper.get_addons_form_data.return_value = [('key', 'value')]
        mock_agent_helper.get_book_fare.return_value = {'currency': 'USD', 'total_price': 100}
        mock_agent_helper.parse_book_result.return_value = {'pnr': 'ABC123'}

        passengers = [{'passenger_type': 'adult', 'first_name': 'Test', 'last_name': 'User'}]

        # 调用book方法，use_dlx_cabin=False
        result = service.book(fare_key='10,ECO_FARE_KEY', passengers=passengers, direct_pay=False, use_dlx_cabin=False)

        # 验证update_baggage_options被调用
        mock_agent_helper.update_baggage_options.assert_called_once_with(
            airline_code='VJ', html_content='预订页面 - 座位安置', passengers=passengers
        )

        # 验证其他方法正常调用
        assert result['pnr'] == 'ABC123'

    def test_run_verify_book_with_dlx_switch_success(self):
        """测试run_verify_book函数成功切换到DLX舱位"""
        params = {
            'airline_code': 'VJ',
            'dep_airport_code': 'BKK',
            'arr_airport_code': 'SGN',
            'dep_date': '2025-05-20',
            'flight_no': 'VJ123',
            'adult': 1,
            'child': 0,
            'infant': 0,
            'currency_code': 'USD',
            'unique_id': 'test-unique-id-123',
            'src_adult_base': 100,
            'src_adult_tax': 50,
            'passengers': [
                {
                    'passenger_type': 'adult',
                    'baggages': [
                        {
                            'aux_type': 'baggage',
                            'weight': 20,
                            'from_select_id': 'lstPaxItem1',
                            'from_select_value': 'lstPaxItem1/123/1/456',
                        }
                    ],
                }
            ],
        }

        # 模拟账户信息
        mock_account = {'username': 'test_user', 'password': 'test_pass'}

        # 模拟AgentService类
        mock_agent_service = MagicMock()
        mock_agent_service.client.host = 'test.domain.com'
        mock_agent_service.client.session.proxies = {'http': 'http://proxy.example.com'}

        # 模拟搜索结果，包含DLX舱位信息
        mock_search_result = {
            'results': [
                {
                    'trips': [
                        {
                            'flight_nos': ['VJ123'],
                            'dep_date': '2025-05-20',
                            'dep_time': '10:00',
                            'fares': {
                                'adult': {'base': 90, 'tax': 40},  # ECO舱位价格
                                'child': {'base': 90, 'tax': 40},
                                'infant': {'base': 90, 'tax': 40},
                            },
                            'all_fares': {  # 所有舱位信息，用托运公斤数做key
                                '0kg': {  # ECO舱位（0kg托运行李）
                                    'adult': {'base': 90, 'tax': 40},
                                    'child': {'base': 90, 'tax': 40},
                                    'infant': {'base': 90, 'tax': 40},
                                    'fare_key': '10,ECO_FARE_KEY',
                                    'cabin': {'cabin_class': 'Eco', 'code': 'Y', 'name': '经济舱'},
                                },
                                '20kg': {  # DLX舱位（20kg托运行李）
                                    'adult': {'base': 120, 'tax': 50},
                                    'child': {'base': 120, 'tax': 50},
                                    'infant': {'base': 120, 'tax': 50},
                                    'fare_key': '8,DLX_FARE_KEY',
                                    'cabin': {'cabin_class': 'Deluxe', 'code': 'W', 'name': '豪华经济舱'},
                                },
                            },
                        }
                    ],
                    'extra': {'fare_key': '10,ECO_FARE_KEY'},
                }
            ]
        }

        # 模拟预订结果
        mock_book_result = {'fare': {'currency': 'USD'}, 'pnr': 'ABC123'}

        # 使用patch装饰器模拟多个函数
        with patch(
            'app.services.agent_service.get_fixed_account', return_value='test_account'
        ) as mock_get_fixed_account, patch(
            'app.services.agent_service.get_account', return_value=mock_account
        ) as mock_get_account, patch(
            'app.services.agent_service.AgentService', return_value=mock_agent_service
        ) as mock_agent_service_class:

            # 设置模拟对象的行为
            mock_agent_service.login.return_value = None
            mock_agent_service.set_proxy.return_value = None
            mock_agent_service.search.return_value = mock_search_result
            mock_agent_service.book.return_value = mock_book_result

            # 调用被测试的函数
            result = agent_service.run_verify_book(params)

            # 验证结果
            assert result['error']['code'] == ApiCodes.SUCCESS.value
            assert result['data']['book']['pnr'] == 'ABC123'

            # 验证book方法被调用时使用了DLX舱位的fare_key和use_dlx_cabin=True
            mock_agent_service.book.assert_called_once()
            call_args = mock_agent_service.book.call_args
            assert call_args[1]['fare_key'] == '8,DLX_FARE_KEY'
            assert call_args[1]['use_dlx_cabin'] is True

    def test_run_verify_book_without_dlx_info(self):
        """测试run_verify_book函数在没有DLX舱位信息时继续使用ECO舱位"""
        params = {
            'airline_code': 'VJ',
            'dep_airport_code': 'BKK',
            'arr_airport_code': 'SGN',
            'dep_date': '2025-05-20',
            'flight_no': 'VJ123',
            'adult': 1,
            'child': 0,
            'infant': 0,
            'currency_code': 'USD',
            'unique_id': 'test-unique-id-123',
            'src_adult_base': 100,
            'src_adult_tax': 50,
            'passengers': [
                {
                    'passenger_type': 'adult',
                    'baggages': [
                        {
                            'aux_type': 'baggage',
                            'weight': 20,
                            'from_select_id': 'lstPaxItem1',
                            'from_select_value': 'lstPaxItem1/123/1/456',
                        }
                    ],
                }
            ],
        }

        # 模拟账户信息
        mock_account = {'username': 'test_user', 'password': 'test_pass'}

        # 模拟AgentService类
        mock_agent_service = MagicMock()
        mock_agent_service.client.host = 'test.domain.com'
        mock_agent_service.client.session.proxies = {'http': 'http://proxy.example.com'}

        # 模拟搜索结果，不包含DLX舱位信息
        mock_search_result = {
            'results': [
                {
                    'trips': [
                        {
                            'flight_nos': ['VJ123'],
                            'dep_date': '2025-05-20',
                            'dep_time': '10:00',
                            'fares': {
                                'adult': {'base': 90, 'tax': 40},  # 只有ECO舱位价格
                                'child': {'base': 90, 'tax': 40},
                                'infant': {'base': 90, 'tax': 40},
                            },
                            'all_fares': {  # 只有0kg舱位，没有20kg
                                '0kg': {  # ECO舱位（0kg托运行李）
                                    'adult': {'base': 90, 'tax': 40},
                                    'child': {'base': 90, 'tax': 40},
                                    'infant': {'base': 90, 'tax': 40},
                                    'fare_key': '10,ECO_FARE_KEY',
                                    'cabin': {'cabin_class': 'Eco', 'code': 'Y', 'name': '经济舱'},
                                }
                            },
                        }
                    ],
                    'extra': {'fare_key': '10,ECO_FARE_KEY'},
                }
            ]
        }

        # 模拟预订结果
        mock_book_result = {'fare': {'currency': 'USD'}, 'pnr': 'ABC123'}

        # 使用patch装饰器模拟多个函数
        with patch('app.services.agent_service.get_fixed_account', return_value='test_account'), patch(
            'app.services.agent_service.get_account', return_value=mock_account
        ), patch('app.services.agent_service.AgentService', return_value=mock_agent_service):

            # 设置模拟对象的行为
            mock_agent_service.login.return_value = None
            mock_agent_service.set_proxy.return_value = None
            mock_agent_service.search.return_value = mock_search_result
            mock_agent_service.book.return_value = mock_book_result

            # 调用被测试的函数
            result = agent_service.run_verify_book(params)

            # 验证结果
            assert result['error']['code'] == ApiCodes.SUCCESS.value
            assert result['data']['book']['pnr'] == 'ABC123'

            # 验证book方法被调用时使用了ECO舱位的fare_key和use_dlx_cabin=False
            mock_agent_service.book.assert_called_once()
            call_args = mock_agent_service.book.call_args
            assert call_args[1]['fare_key'] == '10,ECO_FARE_KEY'
            assert call_args[1]['use_dlx_cabin'] is False

    def test_run_verify_book_price_increase_no_dlx_check(self):
        """测试run_verify_book函数在变价时不进行DLX判断"""
        params = {
            'airline_code': 'VJ',
            'dep_airport_code': 'BKK',
            'arr_airport_code': 'SGN',
            'dep_date': '2025-05-20',
            'flight_no': 'VJ123',
            'adult': 1,
            'child': 0,
            'infant': 0,
            'currency_code': 'USD',
            'unique_id': 'test-unique-id-123',
            'src_adult_base': 100,
            'src_adult_tax': 50,
            'passengers': [
                {
                    'passenger_type': 'adult',
                    'baggages': [
                        {
                            'aux_type': 'baggage',
                            'weight': 20,
                            'from_select_id': 'lstPaxItem1',
                            'from_select_value': 'lstPaxItem1/123/1/456',
                        }
                    ],
                }
            ],
        }

        # 模拟账户信息
        mock_account = {'username': 'test_user', 'password': 'test_pass'}

        # 模拟AgentService类
        mock_agent_service = MagicMock()
        mock_agent_service.client.host = 'test.domain.com'
        mock_agent_service.client.session.proxies = {'http': 'http://proxy.example.com'}

        # 模拟搜索结果，价格高于原价格（变价）
        mock_search_result = {
            'results': [
                {
                    'trips': [
                        {
                            'flight_nos': ['VJ123'],
                            'dep_date': '2025-05-20',
                            'dep_time': '10:00',
                            'fares': {
                                'adult': {'base': 120, 'tax': 60},  # 总价180 > 原价150
                                'child': {'base': 120, 'tax': 60},
                                'infant': {'base': 120, 'tax': 60},
                            },
                            'all_fares': {  # 即使有20kg信息也不应该使用（因为变价）
                                '0kg': {  # ECO舱位（0kg托运行李）
                                    'adult': {'base': 120, 'tax': 60},
                                    'child': {'base': 120, 'tax': 60},
                                    'infant': {'base': 120, 'tax': 60},
                                    'fare_key': '10,ECO_FARE_KEY',
                                    'cabin': {'cabin_class': 'Eco', 'code': 'Y', 'name': '经济舱'},
                                },
                                '20kg': {  # DLX舱位（20kg托运行李）
                                    'adult': {'base': 150, 'tax': 70},
                                    'child': {'base': 150, 'tax': 70},
                                    'infant': {'base': 150, 'tax': 70},
                                    'fare_key': '8,DLX_FARE_KEY',
                                    'cabin': {'cabin_class': 'Deluxe', 'code': 'W', 'name': '豪华经济舱'},
                                },
                            },
                        }
                    ],
                    'extra': {'fare_key': '10,ECO_FARE_KEY'},
                }
            ]
        }

        # 使用patch装饰器模拟多个函数
        with patch('app.services.agent_service.get_fixed_account', return_value='test_account'), patch(
            'app.services.agent_service.get_account', return_value=mock_account
        ), patch('app.services.agent_service.AgentService', return_value=mock_agent_service):

            # 设置模拟对象的行为
            mock_agent_service.login.return_value = None
            mock_agent_service.set_proxy.return_value = None
            mock_agent_service.search.return_value = mock_search_result

            # 调用被测试的函数
            result = agent_service.run_verify_book(params)

            # 验证结果应该是价格错误，不会进行DLX判断
            assert result['error']['code'] == ApiCodes.BOOK_PRICE_ERROR.value
            assert '新总价 180 高于原总价 150' in result['error']['message']

            # 验证book方法没有被调用（因为价格检查失败）
            mock_agent_service.book.assert_not_called()
