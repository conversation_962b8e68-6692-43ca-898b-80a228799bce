import json
import uuid
from unittest.mock import Mock, patch


def test_encryption_service():
    """测试加密服务的基本功能（使用mock避免网络依赖）"""
    # 在test方法内引入依赖避免循环引用
    from app.clients.vj_encryption_service import VJEncryptionService
    from app.clients.vj_client import object_to_query_params

    # 准备测试数据
    test_data = {
        "adultCount": 1,
        "arrival": "SGN",
        "childCount": 0,
        "currency": "usd",
        "daysAfterDeparture": 0,
        "daysBeforeDeparture": 0,
        "departureDate": "2025-03-30",
        "departurePlace": "HAN",
        "infantCount": 0,
        "oneway": 1,
        "requestId": str(uuid.uuid4()),
        "sessionId": None,
        "user-agent-ls-data": str(uuid.uuid4()),
        "x-power-web-s-d": str(uuid.uuid4()),
    }

    # 生成查询字符串
    query_string = object_to_query_params(test_data)

    # Mock加密服务的网络请求和初始化
    with patch('requests.get') as mock_get, patch('requests.post') as mock_post:
        # Mock健康检查请求
        mock_health_response = Mock()
        mock_health_response.status_code = 200
        mock_get.return_value = mock_health_response

        # 模拟成功的加密响应
        mock_encrypt_response = Mock()
        mock_encrypt_response.json.return_value = {'encrypted': 'mocked_encrypted_data_12345', 'status': 'success'}
        mock_encrypt_response.status_code = 200
        mock_post.return_value = mock_encrypt_response

        # 初始化加密服务
        encryption_service = VJEncryptionService()

        # 测试加密
        encrypted_data = encryption_service.encrypt_data(json.dumps(test_data), query_string)

        # 验证结果
        assert encrypted_data is not None
        assert isinstance(encrypted_data, dict)
        assert 'encrypted' in encrypted_data
        assert encrypted_data['encrypted'] == 'mocked_encrypted_data_12345'

        # 验证网络请求被正确调用
        mock_get.assert_called_once()  # 健康检查
        mock_post.assert_called_once()  # 加密请求


if __name__ == "__main__":
    test_encryption_service()
