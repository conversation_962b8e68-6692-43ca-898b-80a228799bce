import pytest
from unittest.mock import MagicMock, patch
from bs4 import BeautifulSoup

from app.services import agent_helper


class TestAgentHelperDLX:
    """测试agent_helper中的DLX舱位功能"""

    def test_parse_flight_with_dlx_cabin_level(self):
        """测试parse_flight函数支持DLX舱位级别"""
        # 模拟HTML内容，包含ECO和DLX舱位信息
        mock_html = """
        <div class="tabsContainer">
            <ul class="tabs_ribbon">
                <li><a id="gridTravelOptDepTab03/06/2025" href="#gridTravelOptDepPane03/06/2025">03/06/2025</a></li>
            </ul>
        </div>
        <div id="gridTravelOptDepPane03/06/2025" class="pane">
            <table class="FlightsGrid">
                <tr id="gridTravelOptDep1">
                    <tr>
                        <td>03/06/2025</td>
                        <td>10:00 BKK</td>
                        <td>12:00 SGN</td>
                        <td><span class="airlineVJ">VJ123</span></td>
                    </tr>
                    <tr>
                        <td data-familyid="Eco">
                            <input id="fare" value="100.00" />
                            <input id="fare_taxes" value="20.00" />
                            <input id="charge_taxes" value="10.00" />
                            <input id="charges" value="5.00" />
                            <input id="total_complete_charges" value="135.00" />
                            <input name="gridTravelOptDep" value="10,ECO_FARE_KEY" />
                        </td>
                        <td data-familyid="Deluxe">
                            <input id="fare" value="150.00" />
                            <input id="fare_taxes" value="30.00" />
                            <input id="charge_taxes" value="15.00" />
                            <input id="charges" value="8.00" />
                            <input id="total_complete_charges" value="203.00" />
                            <input name="gridTravelOptDep" value="8,DLX_FARE_KEY" />
                        </td>
                    </tr>
                </tr>
            </table>
        </div>
        <div class="fareRulesMOver" id="ecofarerules">
            <div class="fareRulesContent">
                <li class="greencheckmark">手提行李 7 公斤</li>
            </div>
        </div>
        <div class="fareRulesMOver" id="deluxefarerules">
            <div class="fareRulesContent">
                <li class="greencheckmark">手提行李 7 公斤</li>
                <li class="greencheckmark">托运行李 20 公斤</li>
            </div>
        </div>
        """

        # 测试默认ECO舱位
        result_eco = agent_helper.parse_flight(
            html=mock_html, date='03/06/2025', passenger_num=1, airline_code='VJ', cabin_level='Eco'
        )

        assert result_eco is not None
        assert len(result_eco['results']) == 1
        flight = result_eco['results'][0]

        # 验证主要fare信息是最低价格舱位（ECO: 100 < DLX: 150）
        assert flight['trips'][0]['fares']['adult']['base'] == 100.0
        assert flight['extra']['fare_key'] == '10,ECO_FARE_KEY'

        # 验证all_fares包含所有舱位信息（新结构：用托运公斤数做key）
        assert 'all_fares' in flight['trips'][0]
        all_fares = flight['trips'][0]['all_fares']
        assert isinstance(all_fares, dict)
        assert '0kg' in all_fares  # ECO舱位（0kg托运行李）
        assert '20kg' in all_fares  # DLX舱位（20kg托运行李）

        # 验证0kg舱位信息（ECO）
        eco_fare = all_fares['0kg']
        assert eco_fare['adult']['base'] == 100.0
        assert eco_fare['fare_key'] == '10,ECO_FARE_KEY'
        assert eco_fare['cabin']['cabin_class'] == 'Eco'

        # 验证20kg舱位信息（DLX）
        dlx_fare = all_fares['20kg']
        assert dlx_fare['adult']['base'] == 150.0
        assert dlx_fare['fare_key'] == '8,DLX_FARE_KEY'
        assert dlx_fare['cabin']['cabin_class'] == 'Deluxe'

        # 注意：由于fares永远存放最低价格舱位，即使指定cabin_level='Deluxe'
        # fares仍然会是ECO舱位（因为ECO价格更低），但all_fares会包含所有舱位信息
        # 这个测试主要验证all_fares字典结构的正确性

    def test_get_fare_info_eco_cabin(self):
        """测试get_fare_info函数获取ECO舱位信息"""
        mock_html = """
        <tr>
            <td data-familyid="Eco">
                <input id="fare" value="100.00" />
                <input id="fare_taxes" value="20.00" />
                <input id="charge_taxes" value="10.00" />
                <input id="charges" value="5.00" />
                <input id="total_complete_charges" value="135.00" />
                <input name="gridTravelOptDep" value="10,ECO_FARE_KEY" />
            </td>
        </tr>
        """

        tr_dom = BeautifulSoup(mock_html, 'html.parser').find('tr')
        result = agent_helper.get_fare_info(tr_dom, passenger_num=1, cabin_level="Eco")

        assert result is not None
        assert result['cabin']['cabin_class'] == 'Eco'
        assert result['cabin']['name'] == '经济舱'
        assert result['adult']['base'] == 100.0
        assert result['adult']['tax'] == 35.0  # (20+10+5)/1
        assert result['adult']['total'] == 135.0
        assert result['fare_key'] == '10,ECO_FARE_KEY'

    def test_get_fare_info_dlx_cabin(self):
        """测试get_fare_info函数获取DLX舱位信息"""
        mock_html = """
        <tr>
            <td data-familyid="Deluxe">
                <input id="fare" value="150.00" />
                <input id="fare_taxes" value="30.00" />
                <input id="charge_taxes" value="15.00" />
                <input id="charges" value="8.00" />
                <input id="total_complete_charges" value="203.00" />
                <input name="gridTravelOptDep" value="8,DLX_FARE_KEY" />
            </td>
        </tr>
        """

        tr_dom = BeautifulSoup(mock_html, 'html.parser').find('tr')
        result = agent_helper.get_fare_info(tr_dom, passenger_num=1, cabin_level="Deluxe")

        assert result is not None
        assert result['cabin']['cabin_class'] == 'Deluxe'
        assert result['cabin']['name'] == '豪华经济舱'
        assert result['adult']['base'] == 150.0
        assert result['adult']['tax'] == 53.0  # (30+15+8)/1
        assert result['adult']['total'] == 203.0
        assert result['fare_key'] == '8,DLX_FARE_KEY'

    def test_get_fare_info_cabin_not_found(self):
        """测试get_fare_info函数在找不到舱位时返回None"""
        mock_html = """
        <tr>
            <td data-familyid="Eco">
                <input id="fare" value="100.00" />
            </td>
        </tr>
        """

        tr_dom = BeautifulSoup(mock_html, 'html.parser').find('tr')
        result = agent_helper.get_fare_info(tr_dom, passenger_num=1, cabin_level="SkyBoss")

        assert result is None

    def test_get_fare_info_cabin_sold_out(self):
        """测试get_fare_info函数在舱位售完时返回None"""
        mock_html = """
        <tr>
            <td data-familyid="Deluxe">售完</td>
        </tr>
        """

        tr_dom = BeautifulSoup(mock_html, 'html.parser').find('tr')
        result = agent_helper.get_fare_info(tr_dom, passenger_num=1, cabin_level="Deluxe")

        assert result is None

    def test_get_baggage_info_dlx_cabin(self):
        """测试get_baggage_info函数获取DLX舱位行李信息"""
        mock_html = """
        <div class="fareRulesMOver" id="deluxefarerules">
            <div class="fareRulesContent">
                <li class="greencheckmark">手提行李 7 公斤</li>
                <li class="greencheckmark">托运行李 20 公斤</li>
            </div>
        </div>
        """

        root_dom = BeautifulSoup(mock_html, 'html.parser')
        result = agent_helper.get_baggage_info(root_dom, cabin_level="Deluxe")

        assert result is not None
        assert 'baggage' in result
        assert 'cabin_baggage' in result['baggage']
        assert 'checked_baggage' in result['baggage']

        # 验证手提行李
        cabin_baggage = result['baggage']['cabin_baggage'][0]
        assert cabin_baggage['weight'] == 7
        assert cabin_baggage['count'] == 1

        # 验证托运行李
        checked_baggage = result['baggage']['checked_baggage'][0]
        assert checked_baggage['weight'] == 20
        assert checked_baggage['count'] == 1
